'use client';

import React from 'react';
import Link from 'next/link';

const QuickCTA = () => {
  return (
    <div className="fixed bottom-6 right-6 z-40">
      {/* Enterprise Minimal CTA */}
      <div style={{
        background: 'var(--sanctuary)',
        border: '1px solid var(--stone)',
        padding: '24px',
        maxWidth: '280px',
        opacity: '0.95'
      }}>
        <div className="text-center">
          <h3 className="card-title" style={{marginBottom: '16px', fontSize: '18px'}}>
            Gotowa na transformację?
          </h3>
          <p className="body-text" style={{marginBottom: '24px', fontSize: '13px'}}>
            Zarezerwuj miejsce na duchowej podróży
          </p>

          <div style={{display: 'flex', flexDirection: 'column', gap: '12px'}}>
            <Link
              href="/kontakt"
              className="btn-ghost btn-primary"
              style={{width: '100%', textAlign: 'center'}}
            >
              Kontakt
            </Link>

            <a
              href="https://wa.me/48123456789?text=Namaste! Interesuje mnie retreat jogi"
              target="_blank"
              rel="noopener noreferrer"
              className="btn-ghost btn-accent"
              style={{width: '100%', textAlign: 'center'}}
            >
              WhatsApp
            </a>
          </div>

          <div className="subtle-text" style={{marginTop: '16px', opacity: '0.6'}}>
            Bali • Sri Lanka • Joga
          </div>
        </div>
      </div>
    </div>
  );
};

export default QuickCTA;