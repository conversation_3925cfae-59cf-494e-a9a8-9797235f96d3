import './globals.css'
import { Playfair_Display, Inter, Cormorant_Garamond } from 'next/font/google'
import ClientNavbar from '@/components/Navbar/ClientNavbar'
import Footer from '@/components/Footer'
import QuickCTA from '@/components/QuickCTA'
import EnterpriseErrorBoundary from '@/components/EnterpriseErrorBoundary'

// Fonts for spiritual minimalism - Oddech w słowach
const cormorant = Cormorant_Garamond({
  subsets: ['latin'],
  weight: ['300', '400'],
  display: 'swap',
  variable: '--font-cormorant'
})

const inter = Inter({
  subsets: ['latin'],
  weight: ['300', '400'],
  display: 'swap',
  variable: '--font-inter'
})

// Zachowane dla kompatybilności
const playfair = Playfair_Display({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-playfair'
})

export const metadata = {
  metadataBase: new URL('https://bakasana-travel.blog'),
  title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka | Transformacyjne Podróże z Julią Jakubowicz',
  description: 'Odkryj duchowe serce Azji na retreatach jogi z Julią Jakubowicz. Bali, Sri Lanka, Ubud, Gili Air - transformacyjne doświadczenia w najpiękniejszych miejscach świata. Joga, medytacja, ayurveda i wewnętrzna przemiana w tropikalnym raju.',
  keywords: 'retreaty jogi bali, joga sri lanka, julia jakubowicz, ubud joga, gili air retreat, transformacyjne podróże, medytacja bali, ayurveda sri lanka, retreat jogi azja, duchowa podróż, joga wakacje, tarasy ryżowe, świątynie bali, lotus joga, om meditation',
  authors: [{ name: 'Julia Jakubowicz' }],
  creator: 'Julia Jakubowicz',
  publisher: 'BAKASANA',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  openGraph: {
    title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka | Om Swastiastu',
    description: 'Transformacyjne retreaty jogi w duchowym sercu Azji. Odkryj magię Bali i Sri Lanki z doświadczoną instruktorką Julią Jakubowicz. Ubud, tarasy ryżowe, świątynie, medytacja i wewnętrzna przemiana.',
    images: [
      {
        url: '/images/background/bali-hero.webp',
        width: 1200,
        height: 630,
        alt: 'Retreat jogi na Bali - tarasy ryżowe Ubud'
      }
    ],
    type: 'website',
    locale: 'pl_PL',
    siteName: 'BAKASANA - Retreaty Jogi',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'BAKASANA - Retreaty Jogi Bali & Sri Lanka',
    description: 'Transformacyjne doświadczenia jogi w najpiękniejszych miejscach Azji. Om Swastiastu! 🕉️',
    images: ['/images/background/bali-hero.webp'],
    creator: '@bakasana_yoga',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
}

export default function RootLayout({ children }) {
  return (
    <html lang="pl" className={`${cormorant.variable} ${inter.variable} ${playfair.variable}`}>
      <head>
        {/* Enterprise Font Loading Strategy */}
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossOrigin="anonymous" />

        {/* Critical fonts with optimal loading - Ultra-minimal weights */}
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Cormorant+Garamond:ital,wght@0,200;0,300;0,400;0,500;1,200;1,300;1,400&display=swap"
          as="style"
          onLoad="this.onload=null;this.rel='stylesheet'"
        />
        <link
          rel="preload"
          href="https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500&display=swap"
          as="style"
          onLoad="this.onload=null;this.rel='stylesheet'"
        />

        {/* Spiritual fonts */}
        <link
          href="https://fonts.googleapis.com/css2?family=Noto+Sans+Devanagari:wght@400;500&display=swap"
          rel="stylesheet"
        />

        {/* Performance optimizations */}
        <link rel="dns-prefetch" href="//images.unsplash.com" />
        <link rel="dns-prefetch" href="//cdn.sanity.io" />

        {/* Enterprise SEO Optimizations */}
        <meta name="robots" content="index, follow, max-image-preview:large, max-snippet:-1, max-video-preview:-1" />
        <meta name="googlebot" content="index, follow" />
        <meta name="bingbot" content="index, follow" />

        {/* Performance hints */}
        <meta httpEquiv="x-dns-prefetch-control" content="on" />
        <meta name="format-detection" content="telephone=no" />

        {/* PWA optimizations */}
        <meta name="theme-color" content="#FDFCF8" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />

        {/* Structured Data for Organization */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: JSON.stringify({
              "@context": "https://schema.org",
              "@type": "Organization",
              "name": "BAKASANA",
              "description": "Transformacyjne retreaty jogi na Bali i Sri Lanka z Julią Jakubowicz",
              "url": "https://bakasana-travel.blog",
              "logo": "https://bakasana-travel.blog/images/logo.png",
              "sameAs": [
                "https://www.instagram.com/fly_with_bakasana",
                "https://www.facebook.com/p/Fly-with-bakasana-100077568306563/"
              ],
              "contactPoint": {
                "@type": "ContactPoint",
                "email": "<EMAIL>",
                "contactType": "customer service"
              },
              "founder": {
                "@type": "Person",
                "name": "Julia Jakubowicz",
                "jobTitle": "Instruktorka Jogi",
                "description": "Doświadczona instruktorka jogi specjalizująca się w retreatach na Bali i Sri Lanka"
              }
            })
          }}
        />

        {/* Critical CSS will be inlined here */}
        <style dangerouslySetInnerHTML={{
          __html: `
            /* Critical above-the-fold styles */
            body {
              font-family: 'Inter', -apple-system, BlinkMacSystemFont, sans-serif;
              background: #FDFCF8;
              color: #3A3A3A;
              margin: 0;
              padding: 0;
            }
            .navigation {
              position: fixed;
              top: 0;
              width: 100%;
              z-index: 100;
              background: transparent;
            }
            .hero {
              height: 100vh;
              min-height: 600px;
              display: flex;
              align-items: center;
              justify-content: center;
              text-align: center;
              background: linear-gradient(135deg, #FDFCF8 0%, #F9F7F2 50%, #F5F3EF 100%);
            }
          `
        }} />
      </head>
      <body style={{background: 'var(--sanctuary)', color: 'var(--charcoal)'}}>
        {/* Skip to content link for accessibility */}
        <a href="#main-content" className="skip-link">
          Przejdź do głównej treści
        </a>

        <EnterpriseErrorBoundary>
          <ClientNavbar />
          <main id="main-content">{children}</main>
          <Footer />
          <QuickCTA />
        </EnterpriseErrorBoundary>
      </body>
    </html>
  )
}