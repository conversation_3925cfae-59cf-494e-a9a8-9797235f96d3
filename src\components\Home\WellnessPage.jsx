'use client';

import React, { useCallback, useMemo } from 'react';
import Image from 'next/image';
import Link from 'next/link';
import {
  Instagram,
  Facebook,
  CalendarCheck,
  MessageCircle
} from 'lucide-react';

import { blogPosts } from '@/data/blogPosts';
import TestimonialSlider from '@/components/TestimonialSlider';
import { 
  getOrganizationStructuredData, 
  getYogaInstructorStructuredData,
  getFAQStructuredData 
} from '@/lib/yogaStructuredData';

// Simple Icon component
const SafeIcon = React.memo(({ Icon, className = '' }) => {
  if (!Icon) return null;
  return <Icon className={`w-6 h-6 ${className}`} />;
});
SafeIcon.displayName = 'SafeIcon';



// =============================================
// ENTERPRISE 11/10 HERO SECTION - ULTRA-MINIMAL PERFECTION
// Sacred simplicity meets Balinese elegance
// =============================================

const EnterpriseHeroSection = React.memo(() => {
  const scrollToRetreatSection = useCallback(() => {
    const element = document.getElementById('retreats');
    if (element) {
      element.scrollIntoView({
        behavior: 'smooth',
        block: 'start',
        inline: 'nearest'
      });
    }
  }, []);

  return (
    <section className="hero">
      <div className="hero-content">
        {/* HERO TITLE - Enterprise specifications */}
        <h1 className="hero-title">
          BAKASANA
        </h1>

        {/* HERO SUBTITLE */}
        <p className="hero-subtitle">
          Wewnętrzna podróż przez duchowe serce Azji
        </p>

        {/* HERO META */}
        <div className="hero-meta">
          Bali • Sri Lanka
        </div>

        {/* SACRED QUOTE */}
        <div className="hero-cta">
          <blockquote className="sacred-quote">
            "W miejscu, gdzie się kończy droga, <br />
            tam zaczyna się ścieżka do siebie"
          </blockquote>
          <cite className="subtle-text" style={{marginTop: '16px', display: 'block'}}>
            — Stara Balijaska Mądrość
          </cite>
        </div>

        {/* SUBTLE CTA */}
        <div style={{marginTop: '80px'}}>
          <button
            onClick={scrollToRetreatSection}
            className="subtle-link"
          >
            Dowiedz się więcej
          </button>

          {/* Scroll indicator */}
          <div style={{marginTop: '32px', opacity: '0.4'}}>
            <div style={{width: '1px', height: '24px', background: 'var(--stone)', margin: '0 auto 8px auto', opacity: '0.4'}}></div>
            <svg
              style={{width: '12px', height: '12px', color: 'var(--stone)', opacity: '0.4', margin: '0 auto', display: 'block'}}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M19 14l-7 7m0 0l-7-7m7 7V3" />
            </svg>
          </div>
        </div>
      </div>
    </section>
  );
});
EnterpriseHeroSection.displayName = 'EnterpriseHeroSection';

// =============================================
// ENTERPRISE 11/10 RETREAT CARD - SACRED MINIMALISM
// Museum-quality design meets spiritual elegance  
// =============================================

const EnterpriseRetreatCard = React.memo(({
  title,
  description,
  link,
  imageUrl,
  price,
  location,
  duration,
  badge,
  className = ''
}) => {
  return (
    <article className={`group relative ${className}`}>
      {/* VISUAL FOUNDATION - Sacred imagery */}
      {imageUrl && (
        <div className="relative aspect-[5/4] overflow-hidden mb-8">
          <Image
            src={imageUrl}
            alt={`${title} - Sacred retreat sanctuary`}
            fill
            className="object-cover object-center transition-transform duration-500 group-hover:scale-105"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            quality={95}
          />
          
          {/* Sacred gradient overlay */}
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
          
          {/* Badge - Cultural marker */}
          {badge && (
            <div className="absolute top-6 left-6">
              <span className="px-3 py-1 bg-white/95 text-charcoal text-xs font-light tracking-[0.1em] uppercase backdrop-blur-sm">
                {badge}
              </span>
            </div>
          )}
        </div>
      )}

      {/* CONTENT HIERARCHY - Sacred typography */}
      <div className="space-y-6">
        
        {/* Location & Duration - Sacred details */}
        <div className="flex items-center justify-between text-sm">
          {location && (
            <div className="flex items-center gap-2 text-sage-dark font-light tracking-wide">
              <span className="w-1 h-1 bg-sage-dark rounded-full"></span>
              <span>{location}</span>
            </div>
          )}
          {duration && (
            <span className="text-charcoal/60 font-light tracking-wide">{duration}</span>
          )}
        </div>

        {/* Title - Sacred heading */}
        <div className="space-y-3">
          <h3 className="text-2xl md:text-3xl font-light text-charcoal leading-tight tracking-wide font-serif">
            {link ? (
              <Link
                href={link}
                className="hover:text-sage-dark transition-colors duration-200"
              >
                {title}
              </Link>
            ) : (
              title
            )}
          </h3>
          
          {/* Cena UKRYTA! Tylko mały link "Szczegóły" w rogu */}
          <div className="text-right">
            <a
              href={link || '#'}
              className="text-xs font-light text-stone/60 tracking-wide uppercase hover:opacity-70 transition-opacity duration-200"
            >
              Szczegóły
            </a>
          </div>
        </div>

        {/* Description - Sacred narrative */}
        <p className="text-charcoal/80 font-light leading-relaxed tracking-wide max-w-md">
          {description}
        </p>

        {/* Sacred action */}
        {link && (
          <div className="pt-4">
            <Link
              href={link}
              className="group/link inline-flex items-center gap-2 text-sm font-light tracking-[0.1em] uppercase text-sage-dark hover:text-charcoal transition-colors duration-200"
            >
              <span>Dowiedz się więcej</span>
              <svg 
                className="w-4 h-4 transition-transform duration-200 group-hover/link:translate-x-1" 
                fill="none" 
                stroke="currentColor" 
                viewBox="0 0 24 24"
              >
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M17 8l4 4m0 0l-4 4m4-4H3" />
              </svg>
            </Link>
          </div>
        )}

      </div>
    </article>
  );
});
EnterpriseRetreatCard.displayName = 'EnterpriseRetreatCard';

// Ultra-Minimal Section Divider
const SectionDivider = React.memo(() => {
  return (
    <div className="section-divider my-24"></div>
  );
});
SectionDivider.displayName = 'SectionDivider';





// Main WellnessPage Component - Ultra-Minimalist
const WellnessPage = ({ latestPosts }) => {
  const posts = useMemo(() => latestPosts || blogPosts.slice(0, 3), [latestPosts]);

  // Enhanced retreat highlights with more detailed information
  const retreatHighlights = useMemo(() => [
    {
      id: 'ubud',
      title: 'Sanktuarium Ubud',
      description: 'Siedem dni duchowego zanurzenia w kulturowym sercu Bali. Odkryj swoją wewnętrzną mądrość wśród starożytnych tarasów ryżowych i świętych świątyń.',
      imageUrl: '/images/retreats/ubud.webp',
      location: 'Ubud, Bali',
      duration: '7 dni',
      price: '€2,400',
      badge: 'Popularne',
      highlights: [
        'Codzienna joga i medytacja',
        'Wycieczki do świętych świątyń',
        'Warsztaty z filozofii jogi',
        'Kąpiele w górskich źródłach',
        'Certyfikowane zakwaterowanie'
      ]
    },
    {
      id: 'gili-air',
      title: 'Raj Gili Air',
      description: 'Pięć dni czystego spokoju na rajskiej wyspie, gdzie czas płynie wolno, a pokój przychodzi naturalnie. Odnowa w towarzystwie inspirujących kobiet.',
      imageUrl: '/images/retreats/gili.webp',
      location: 'Gili Air, Indonezja',
      duration: '5 dni',
      price: '€1,800',
      badge: 'Ekskluzywne',
      highlights: [
        'Plaża prywatna tylko dla grupy',
        'Snorkeling z żółwiami',
        'Yoga na wschodzie słońca',
        'Masaże balijskie',
        'Maksymalnie 6 osób'
      ]
    },
    {
      id: 'canggu',
      title: 'Klify Canggu',
      description: 'Dziesięć dni transformacji z sesjami jogi na klifach z widokiem na nieskończony ocean. Spektakularne zachody słońca i wewnętrzna przemiana.',
      imageUrl: '/images/retreats/canggu.webp',
      location: 'Canggu, Bali',
      duration: '10 dni',
      price: '€3,200',
      badge: 'Intensywne',
      highlights: [
        'Joga na klifach o zachodzie',
        'Warsztaty transformacji',
        'Surfing dla początkujących',
        'Ceremonie pełni księżyca',
        'Indywidualne sesje coaching'
      ]
    },
    {
      id: 'sri-lanka',
      title: 'Perła Sri Lanka',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk w otoczeniu dziewiczej przyrody.',
      imageUrl: '/images/retreats/sri-lanka.webp',
      location: 'Ella, Sri Lanka',
      duration: '8 dni',
      price: '€2,800',
      badge: 'Nowość',
      highlights: [
        'Authentic Ayurvedic treatments',
        'Buddhist meditation sessions',
        'Tea plantation visits',
        'Elephant sanctuary experience',
        'Traditional Sri Lankan cuisine'
      ]
    }
  ], []);

  // Enhanced testimonials with more details
  const testimonials = useMemo(() => [
    {
      quote: "To była najbardziej transformująca podróż mojego życia. Julia stworzyła przestrzeń ciepła i bezpieczeństwa, która pozwoliła mi prawdziwie połączyć się z sobą. Wróciłam jako zupełnie nowa kobieta, pełna pewności siebie i wewnętrznego spokoju.",
      author: "Anna Kowalska",
      location: "Warszawa",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/anna.webp"
    },
    {
      quote: "Idealny balans jogi, kultury i relaksu. Julia ma dar tworzenia magicznych chwil, które zostają w pamięci na zawsze. Nasza grupa stała się jak druga rodzina - kobiety wspierające się nawzajem w drodze do siebie.",
      author: "Katarzyna Nowak",
      location: "Gdańsk",
      rating: 5,
      retreat: "Raj Gili Air",
      avatar: "/images/testimonials/kasia.webp"
    },
    {
      quote: "Każdy dzień przynosił nowe odkrycia i głębsze zrozumienie siebie. Julia prowadzi z taką mądrością i empatią, że czujesz się bezpiecznie na każdym kroku. To nie był tylko retreat - to była prawdziwa podróż do siebie.",
      author: "Marta Wiśniewska",
      location: "Wrocław",
      rating: 5,
      retreat: "Klify Canggu",
      avatar: "/images/testimonials/marta.webp"
    },
    {
      quote: "Bali z Julią to nie tylko joga - to odkrywanie swojej wewnętrznej siły i potencjału. Wróciłam z nową energią, spokojem wewnętrznym i pewnością, że mogę wszystko. Polecam każdej kobiecie, która szuka prawdziwej zmiany.",
      author: "Agnieszka Zielińska",
      location: "Kraków",
      rating: 5,
      retreat: "Sanktuarium Ubud",
      avatar: "/images/testimonials/agnieszka.webp"
    }
  ], []);

  // Enhanced FAQs with more comprehensive answers
  const faqs = useMemo(() => [
    {
      question: "Czy retreaty są odpowiednie dla początkujących w jodze?",
      answer: "Absolutnie tak! Nasze retreaty przyjmują kobiety na każdym poziomie doświadczenia. Julia prowadzi każdą sesję z uwagą na indywidualne potrzeby i możliwości. Oferujemy modyfikacje dla każdego ćwiczenia, więc każda znajdzie swoje miejsce w naszym kręgu, niezależnie od poziomu zaawansowania."
    },
    {
      question: "Co dokładnie jest wliczone w cenę retreatu?",
      answer: "Cena obejmuje pełne zakwaterowanie w starannie wybranych miejscach, wszystkie wegetariańskie/wegańskie posiłki przygotowane z lokalnych składników, codzienne sesje jogi i medytacji, wycieczki kulturalne z przewodnikiem, transfery lotniskowe, ubezpieczenie grupowe oraz wsparcie Julii przez całą podróż. Jedyne dodatkowe koszty to loty i ewentualne zakupy osobiste."
    },
    {
      question: "Jakie są terminy najbliższych retreatów?",
      answer: "Nasze nadchodzące retreaty zaplanowane są na: czerwiec 2024 (Ubud), lipiec 2024 (Gili Air), wrzesień 2024 (Canggu) oraz październik 2024 (Sri Lanka - nowość!). Szczegółowe daty i dostępność miejsc znajdziesz w sekcji kalendarza. Rezerwacja miejsc odbywa się z wyprzedzeniem 3-6 miesięcy."
    },
    {
      question: "Czy muszę mieć wcześniejsze doświadczenie w medytacji?",
      answer: "Nie, żadne wcześniejsze doświadczenie nie jest konieczne. Nasze retreaty są idealne zarówno dla osób rozpoczynających swoją duchową podróż, jak i dla bardziej zaawansowanych praktyków. Julia wprowadza techniki medytacji stopniowo, zawsze dostosowując je do grupy i indywidualnych potrzeb każdej uczestniczki."
    },
    {
      question: "Jak duże są grupy na retreatach?",
      answer: "Nasze grupy są świadomie małe i intymne - zazwyczaj 6-12 uczestniczek, maksymalnie 15 osób. To zapewnia indywidualną uwagę Julii, możliwość nawiązania głębokich, znaczących połączeń z innymi kobietami oraz stworzenie bezpiecznej przestrzeni dla osobistych przemian."
    },
    {
      question: "Jakie są warunki pogodowe i co spakować?",
      answer: "Bali i Sri Lanka cieszą się tropikalnym klimatem przez cały rok. Temperatura wynosi 26-32°C, z możliwością opadów (szczególnie październik-marzec). Przygotowujemy szczegółową listę rzeczy do spakowania dla każdej uczestniczki, uwzględniającą specyfikę konkretnego retreatu i pory roku."
    }
  ], []);

  // Poetyckie retreaty - Duchowa skromność zamiast turystyki
  const baliSriLankaRetreats = useMemo(() => [
    {
      id: 'ubud-march-2025',
      type: 'Wiosenna Transformacja',
      title: 'Serce Dżungli', // Poetycko zamiast "Sanktuarium Ubud"
      startDate: '15 marca 2025',
      endDate: '27 marca 2025',
      location: 'Ubud, Bali',
      participants: 6,
      maxParticipants: 12,
      price: '', // UKRYTA! Tylko mały link "Szczegóły" w rogu
      originalPrice: '',
      description: 'Odkryj energię miejsca wśród mglistych poranków w dżungli. Gdzie starożytne tarasy ryżowe szepczą o wiecznej mądrości, a każdy oddech łączy Cię z duchem Bali.',
      available: true,
      status: 'early-bird',
      highlights: ['Poranne medytacje w mglistej dżungli', 'Ceremonie oczyszczenia duszy', 'Spotkania z lokalną mądrością', 'Duchowe wędrówki'],
      accommodation: 'Sanktuarium wśród drzew',
      meals: 'Pokarm dla duszy',
      activities: ['Wschód słońca z jogą', 'Wędrówki do świętych miejsc', 'Spotkania z lokalnymi mistrzami', 'Rytuały oczyszczenia'],
      imageUrl: '/images/gallery/ubud-rice-terraces.webp',
      culturalElements: ['Om Swastiastu pozdrowienia', 'Balijskie ofiary Penjor', 'Nauka podstaw bahasa Indonesia']
    },
    {
      id: 'sri-lanka-june-2025',
      type: 'Perła Oceanu Indyjskiego',
      title: 'Gdzie Ocean Spotyka Duszę', // Poetycko dla Sri Lanka
      startDate: '10 czerwca 2025',
      endDate: '22 czerwca 2025',
      location: 'Sri Lanka',
      participants: 4,
      maxParticipants: 10,
      price: '', // UKRYTA! Tylko mały link "Szczegóły" w rogu
      description: 'Odkryj starożytną mądrość Ajurwedy w miejscu, gdzie buddyjskie świątynie szepcą o wieczności. Perła oceanu, gdzie każda fala niesie uzdrowienie.',
      available: true,
      status: 'new',
      highlights: ['Ajurwedyjskie rytuały uzdrowienia', 'Medytacje w świątyniach', 'Mądrość starożytnych mistrzów', 'Harmonia z naturą'],
      accommodation: 'Sanktuarium nad oceanem',
      meals: 'Ajurwedyjskie eliksiry życia',
      activities: ['Wschód słońca nad oceanem', 'Rytuały uzdrowienia', 'Pielgrzymki do świętych miejsc', 'Spotkania z mnichami'],
      imageUrl: '/images/gallery/sri-lanka-temple.webp',
      culturalElements: ['Ayubowan pozdrowienia', 'Ceremonie buddyjskie', 'Nauka podstaw sinhala']
    },
    {
      id: 'gili-air-september-2025',
      type: 'Rajska Odnowa',
      title: 'Wyspa Bez Czasu', // Poetycko dla Gili Air
      startDate: '5 września 2025',
      endDate: '12 września 2025',
      location: 'Gili Air',
      participants: 3,
      maxParticipants: 8,
      price: '', // UKRYTA!
      description: 'Miejsce, gdzie czas płynie jak ocean - wolno i spokojnie. Wyspa bez samochodów, gdzie jedynym dźwiękiem są fale i śpiew ptaków.',
      available: true,
      status: 'filling-fast',
      highlights: ['Taniec z żółwiami morskimi', 'Medytacje na białym piasku', 'Rytuały wschodu słońca', 'Cisza bez granic'],
      accommodation: 'Bungalowy nad oceanem',
      meals: 'Dary oceanu i tropików',
      activities: ['Nurkowanie z koralowcami', 'Joga o zachodzie', 'Wędrówki rowerowe', 'Głębokie oddychanie', 'Tradycyjne łodzie'],
      imageUrl: '/images/gallery/gili-air-beach.webp',
      culturalElements: ['Sasak tradycje', 'Lombok kultura', 'Życie bez samochodów']
    },
    {
      id: 'uluwatu-november-2025',
      type: 'Klify i Świątynie',
      title: 'Na Krawędzi Nieskończoności', // Poetycko dla Uluwatu
      startDate: '15 listopada 2025',
      endDate: '25 listopada 2025',
      location: 'Uluwatu, Bali',
      participants: 8,
      maxParticipants: 14,
      price: '', // UKRYTA!
      description: 'Miejsce, gdzie ziemia spotyka się z niebem, a świątynia stoi na krawędzi wieczności. Klify Uluwatu szepczą o starożytnej mądrości.',
      available: true,
      status: 'confirmed',
      highlights: ['Wschody słońca nad oceanem', 'Święte świątynie na klifach', 'Taniec ognia Kecak', 'Harmonia z falami'],
      accommodation: 'Sanktuarium na klifach',
      meals: 'Czysta energia natury',
      activities: ['Joga na krawędzi świata', 'Pielgrzymki do świątyń', 'Taniec duchów o zachodzie', 'Surfowanie z falami', 'Rytuały uzdrowienia'],
      imageUrl: '/images/gallery/uluwatu-temple.webp',
      culturalElements: ['Pura Luhur Uluwatu', 'Taniec Kecak', 'Balijskie ceremonie']
    },
    {
      id: 'sri-lanka-october-2024',
      type: 'Ayurveda & Mindfulness',
      title: 'Perła Sri Lanka',
      startDate: '15 października 2024',
      endDate: '23 października 2024',
      location: 'Ella, Sri Lanka',
      participants: 0,
      maxParticipants: 10,
      price: '€2,800',
      description: 'Nowy wymiar duchowości w perle Oceanu Indyjskiego. Odkryj starożytną mądrość Ajurwedy i buddyjskich praktyk.',
      available: true,
      status: 'new',
      highlights: ['Authentic Ayurveda', 'Buddhist meditation', 'Tea plantation visits'],
      accommodation: 'Boutique mountain resort',
      meals: 'Traditional Sri Lankan & Ayurvedic',
      activities: ['Terapie ajurwedyjskie', 'Wizyty w świątyniach', 'Sanktuarium słoni', 'Podróże koleją']
    }
  ], []);

  // Enhanced social links with more platforms
  const socialLinks = useMemo(() => [
    { 
      id: 'instagram', 
      href: 'https://www.instagram.com/fly_with_bakasana', 
      label: 'Instagram', 
      icon: Instagram,
      description: 'Codzienne inspiracje i zdjęcia z naszych podróży'
    },
    { 
      id: 'facebook', 
      href: 'https://www.facebook.com/p/Fly-with-bakasana-100077568306563/', 
      label: 'Facebook', 
      icon: Facebook,
      description: 'Dołącz do naszej społeczności na Facebooku'
    },
    { 
      id: 'bookings', 
      href: 'https://app.fitssey.com/Flywithbakasana/frontoffice', 
      label: 'Rezerwacje', 
      icon: CalendarCheck,
      description: 'Zarezerwuj swoje miejsce na retreatach'
    },
    {
      id: 'whatsapp',
      href: 'https://wa.me/48123456789',
      label: 'WhatsApp',
      icon: MessageCircle,
      description: 'Bezpośredni kontakt z Julią'
    }
  ], []);

  return (
    <div className="wellness-page bg-white">
      <EnterpriseHeroSection />

      {/* =============================================
          ENTERPRISE 11/10 RETREATS SECTION - SACRED SHOWCASE
          Museum-quality presentation of transformational journeys
          ============================================= */}
      <section id="retreats" className="py-24 md:py-32">
        <div className="max-w-7xl mx-auto px-8 lg:px-12">
          
          {/* DUCHOWE WPROWADZENIE - Poetyka zamiast cen */}
          <div className="text-center mb-20 max-w-4xl mx-auto">
            <div className="mb-8">
              <h2 className="font-cormorant text-4xl md:text-5xl lg:text-6xl font-light text-charcoal tracking-wide leading-tight mb-6">
                Duchowe Sanktuaria
              </h2>
              <div className="w-24 h-px bg-sage-dark mx-auto mb-8"></div>
              <p className="text-lg md:text-xl text-charcoal/80 font-light leading-relaxed tracking-wide max-w-3xl mx-auto">
                Miejsca, gdzie dusza spotyka się z wiecznością. <br />
                Gdzie każdy oddech łączy Cię z mądrością starożytnych mistrzów.
              </p>
            </div>
            
            {/* Sacred Trinity - Cultural markers */}
            <div className="flex flex-col md:flex-row items-center justify-center gap-6 md:gap-12 text-charcoal/60 text-sm font-light tracking-[0.15em] uppercase">
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-temple-dark rounded-full opacity-60"></span>
                <span>Bali - Wyspa Bogów</span>
              </div>
              <div className="w-px h-4 bg-charcoal/20 hidden md:block"></div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-sage-dark rounded-full opacity-60"></span>
                <span>Sri Lanka - Perła Oceanu</span>
              </div>
              <div className="w-px h-4 bg-charcoal/20 hidden md:block"></div>
              <div className="flex items-center gap-2">
                <span className="w-2 h-2 bg-ocean rounded-full opacity-60"></span>
                <span>Duchowa Przemiana</span>
              </div>
            </div>
          </div>

          {/* SACRED GALLERY - Featured Retreats */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 lg:gap-20 mb-24">
            {baliSriLankaRetreats.slice(0, 4).map((retreat) => (
              <EnterpriseRetreatCard
                key={retreat.id}
                title={retreat.title}
                description={retreat.description}
                imageUrl={retreat.imageUrl}
                link={`/program/${retreat.id}`}
                location={retreat.location}
                price={retreat.price}
                duration={retreat.duration}
                badge={retreat.badge}
              />
            ))}
          </div>

          {/* SACRED DIVIDER */}
          <div className="flex items-center justify-center my-16">
            <div className="flex items-center gap-4 text-sage-dark/60">
              <div className="w-12 h-px bg-sage-dark/30"></div>
              <span className="text-2xl opacity-60">ॐ</span>
              <div className="w-12 h-px bg-sage-dark/30"></div>
            </div>
          </div>

          {/* SACRED INVITATION - Call to action */}
          <div className="text-center space-y-8">
            <div className="space-y-4">
              <p className="text-lg text-charcoal/70 font-light italic tracking-wide">
                "Każda podróż zaczyna się od jednego kroku..."
              </p>
              <p className="text-sm text-sage-dark font-light tracking-[0.1em] uppercase">
                Om Swastiastu - Niech pokój będzie z Tobą
              </p>
            </div>
            
            <Link
              href="/program"
              className="btn-ghost btn-primary"
            >
              Odkryj Wszystkie Retreaty
            </Link>
          </div>
          
        </div>
      </section>

      <SectionDivider />

      {/* Destinations Section - Enterprise Poetic Cards */}
      <section className="destinations">
        <div className="text-center mb-16">
          <h2 className="section-header">
            Duchowe Sanktuaria
          </h2>
          <p className="body-text" style={{maxWidth: '600px', margin: '32px auto 0 auto'}}>
            Miejsca, gdzie dusza spotyka się z wiecznością
          </p>
        </div>

        <div className="destinations-grid">
          {/* Bali - Poetic Card */}
          <div className="destination-card">
            <div className="card-image" style={{backgroundImage: 'url(/images/destinations/bali-overview.webp)'}}></div>
            <div className="card-content">
              <div className="card-meta">Ubud • Kultura • Natura</div>
              <h3 className="card-title">Serce Dżungli</h3>
              <p className="card-description body-text">
                Gdzie starożytne świątynie hinduistyczne sąsiadują z terasami ryżowymi Tegallalang.
                Duchowe serce Azji w otoczeniu żywej kultury balijskiej.
              </p>
              <a href="/program" className="card-details">Szczegóły</a>
            </div>
          </div>

          {/* Sri Lanka - Poetic Card */}
          <div className="destination-card">
            <div className="card-image" style={{backgroundImage: 'url(/images/destinations/sri-lanka-overview.webp)'}}></div>
            <div className="card-content">
              <div className="card-meta">Sigiriya • Ajurweda • Buddyzm</div>
              <h3 className="card-title">Gdzie Ocean Spotyka Duszę</h3>
              <p className="card-description body-text">
                Kolebka buddyzmu i ayurvedy, gdzie tysiącletnia mądrość przeplatają się z dziewiczą przyrodą.
                Sigiriya, Kandy i Ella w idealnej harmonii.
              </p>
              <a href="/program/srilanka" className="card-details">Szczegóły</a>
            </div>
          </div>

          {/* Gili Air - Poetic Card */}
          <div className="destination-card">
            <div className="card-image" style={{backgroundImage: 'url(/images/destinations/gili-air.webp)'}}></div>
            <div className="card-content">
              <div className="card-meta">Gili Air • Cisza • Prostota</div>
              <h3 className="card-title">Wyspa Bez Czasu</h3>
              <p className="card-description body-text">
                Miejsce, gdzie czas zatrzymuje się, a jedynym dźwiękiem jest szum oceanu.
                Rajska wyspa dla głębokiej praktyki i wewnętrznej ciszy.
              </p>
              <a href="/program" className="card-details">Szczegóły</a>
            </div>
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* About Julia - Enterprise Authenticity */}
      <section className="about-julia">
        <div className="about-container">
          {/* Image */}
          <div className="enterprise-image">
            <Image
              src="/images/profile/omnie-opt.webp"
              alt="Julia Jakubowicz - Duchowa przewodniczka"
              fill
              className="about-image object-cover"
              sizes="(max-width: 1024px) 100vw, 50vw"
              quality={95}
            />
          </div>

          {/* Content */}
          <div className="about-content">
            <h2 className="section-header" style={{marginBottom: '32px'}}>
              Julia
            </h2>

            {/* Intro Quote */}
            <p className="about-intro">
              "Prawdziwa praktyka zaczyna się, gdy schodzimy z maty. To tam, w codziennym życiu,
              odkrywamy naszą prawdziwą siłę i mądrość, która od zawsze w nas mieszka."
            </p>

            {/* Body Text */}
            <div className="body-text" style={{marginBottom: '32px'}}>
              Moja droga z jogą zaczęła się w Rishikesh, gdzie po raz pierwszy poczułam,
              że praktyka to coś więcej niż ćwiczenia. To sposób życia, sposób oddychania,
              sposób bycia w świecie.
            </div>

            <div className="body-text">
              <span className="temple-gold-text">Bali i Sri Lanka stały się moimi duchowymi domami</span> - miejscami, gdzie
              każdego dnia uczę się od lokalnych mistrzów, że prawdziwa mądrość mieszka w prostocie.
            </div>

            {/* Spiritual Statistics */}
            <div className="spiritual-stats">
              <div className="stat-item">
                <div className="stat-symbol">∞</div>
                <div className="stat-label">Chwil transformacji</div>
              </div>
              <div className="stat-item">
                <div className="stat-symbol">♡</div>
                <div className="stat-label">Otwartych serc</div>
              </div>
              <div className="stat-item">
                <div className="stat-symbol">ॐ</div>
                <div className="stat-label">Duchowych podróży</div>
              </div>
            </div>

            {/* Sacred Places */}
            <div className="subtle-text" style={{textAlign: 'center', marginTop: '32px', opacity: '0.6'}}>
              Rishikesh • Ubud • Kandy • Gili Air • Uluwatu
            </div>
          </div>
        </div>
      </section>

      <SectionDivider />

      {/* Testimonials - Bali/Sri Lanka Inspired */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="display-title text-4xl font-didot font-light mb-8 text-warmCharcoal">
            Głosy Naszych Yoginek
          </h2>
        </div>

        <TestimonialSlider testimonials={testimonials} />
      </section>

      <SectionDivider />

      {/* Kontakt - Duchowa prostota */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="font-cormorant text-4xl font-light mb-8 text-charcoal">
            Skontaktuj się z nami
          </h2>
          <p className="text-lg text-charcoal font-light max-w-2xl mx-auto leading-relaxed mb-6">
            Gotowa na transformację? Masz pytania?
          </p>
          <div className="text-stone/70 text-sm font-light tracking-wide">
            <span className="text-lg opacity-60">ॐ</span>
            <span className="mx-3">Czekamy na Ciebie z otwartym sercem</span>
            <span className="text-lg opacity-60">ॐ</span>
          </div>
        </div>

        {/* Uproszczone linki kontaktowe - maksymalnie 4 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-12 mb-16 max-w-2xl mx-auto">
          <a
            href="https://www.instagram.com/fly_with_bakasana"
            target="_blank"
            rel="noopener noreferrer"
            className="text-center p-6 hover:opacity-70 transition-opacity duration-200"
          >
            <h3 className="font-light text-charcoal mb-2 tracking-wide">Instagram</h3>
            <p className="text-sm text-stone font-light">Codzienne inspiracje</p>
          </a>

          <a
            href="mailto:<EMAIL>"
            className="text-center p-6 hover:opacity-70 transition-opacity duration-200"
          >
            <h3 className="font-light text-charcoal mb-2 tracking-wide">Email</h3>
            <p className="text-sm text-stone font-light">Bezpośredni kontakt</p>
          </a>
        </div>
      </section>

      <SectionDivider />

      {/* Inspiracje - Duchowe historie */}
      <section className="container">
        <div className="text-center mb-16">
          <h2 className="font-cormorant text-4xl font-light mb-8 text-charcoal">
            Inspiracje z Azji
          </h2>
          <p className="text-lg text-charcoal font-light max-w-2xl mx-auto leading-relaxed mb-6">
            Historie transformacji i mądrość czerpana z najpiękniejszych miejsc Bali i Sri Lanki.
          </p>
          <div className="text-stone/60 text-xs font-light tracking-wide uppercase">
            <span>Inspiracje</span>
            <span className="mx-3">•</span>
            <span>Podróże</span>
            <span className="mx-3">•</span>
            <span>Joga</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-3 gap-16">
          {posts.map((post) => (
            <EnterpriseRetreatCard
              key={post.slug} // Używamy slug jako klucza zamiast id
              title={post.title}
              description={post.excerpt}
              link={`/blog/${post.slug}`}
              imageUrl={post.imageUrl} // Poprawka: imageUrl zamiast image
              className="blog-card"
            />
          ))}
        </div>

        <div className="text-center mt-16">
          <Link
            href="/blog"
            className="text-sm font-light tracking-wide uppercase text-stone hover:opacity-70 transition-opacity duration-200"
          >
            Więcej Inspiracji
          </Link>
        </div>
      </section>
      
      {/* Structured Data for SEO */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getOrganizationStructuredData())
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getYogaInstructorStructuredData())
        }}
      />
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(getFAQStructuredData(faqs))
        }}
      />
    </div>
  );
};

export default WellnessPage;