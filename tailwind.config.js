/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{js,jsx}'],
  theme: {
    extend: {
      colors: {
        // PALETA SPOKOJU - Duchowa Skromność
        // Główne kolory sanktuarium
        sanctuary: '#FDFCF8',        // Mleczny - główne tło (nie biały!)
        charcoal: '#3A3A3A',         // Ciepły antracyt - główny tekst (nie czarny!)
        warmCharcoal: '#3A3432',     // Zachowane dla kompatybilności
        stone: '#8A8A8A',            // Szary kamień - drugorządne elementy
        whisper: '#F9F7F2',          // Bardzo jasny beż - footer i subtelne tła

        // Delikatne akcenty duchowe
        sage: {
          DEFAULT: '#7C9885',        // Zachowane - tarasy ryżowe
          light: '#A8B5A8',
          dark: '#5A6B5D',
        },
        temple: {
          DEFAULT: '#C4A575',        // Delikatny złoty (zmieniony z #C9A961)
          light: '#E6C78A',
          dark: '#A68B4B',
        },
        ocean: {
          DEFAULT: '#4A6B7C',        // Zachowane - ocean Sri Lanka
          light: '#7A9BAC',
        },
        earth: {
          DEFAULT: '#8B6F47',        // Zachowane - ziemia Bali
          light: '#B5956F',
        },

        // Subtelne duchowe elementy
        lotus: '#E8D5D0',            // Zachowane - kwiat lotosu
        rice: '#F7F5F0',             // Zachowane - pola ryżowe
        bamboo: '#9CAF88',           // Zachowane - bambus
        om: '#D4A574',               // Zachowane - symbol Om
        mandala: '#8B7B9B',          // Zachowane - mandala
        incense: '#A8A5A0',          // Zachowane - kadzidło

        // Aliasy dla łatwiejszego użycia
        warmWhite: '#FDFCF8',        // Alias dla sanctuary
        primary: '#3A3A3A',          // Alias dla charcoal
        secondary: '#8A8A8A',        // Alias dla stone
        accent: '#C4A575',           // Alias dla temple.DEFAULT
      },
      fontFamily: {
        // Duchowa typografia - Oddech w słowach
        'cormorant': ['Cormorant Garamond', 'Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'inter': ['Inter', 'Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],

        // Zachowane dla kompatybilności
        'playfair': ['var(--font-playfair)', 'Playfair Display', 'serif'],
        'didot': ['Didot', 'Bodoni MT', 'Playfair Display', 'serif'],
        'helvetica': ['Helvetica Neue', '-apple-system', 'BlinkMacSystemFont', 'sans-serif'],
      },
      fontSize: {
        'hero': ['4rem', { lineHeight: '1.1', letterSpacing: '0.2em' }],
        'display': ['2.5rem', { lineHeight: '1.2', letterSpacing: '0.02em' }],
        'heading': ['1.5rem', { lineHeight: '1.3', letterSpacing: '0.02em' }],
        'body': ['1rem', { lineHeight: '1.8', fontWeight: '300' }],
        'caption': ['0.875rem', { lineHeight: '1.6', letterSpacing: '0.05em' }],
        'micro': ['0.75rem', { lineHeight: '1.5', letterSpacing: '0.1em' }],
      },
      spacing: {
        'section': '120px',      // 120px między sekcjami minimum
        'container': '8%',       // 8% marginesy boczne
        'breathe': '180px',      // Hojne odstępy dla luksusowego odczucia
        'element': '2rem',       // Wewnętrzne paddingi elementów
        'card': '3rem',          // Paddingi kart
      },
      container: {
        center: true,
        padding: '8%',           // 8% marginesy boczne - przestrzeń jako luksus
        screens: {
          sm: '640px',
          md: '768px',
          lg: '1024px',
          xl: '1200px',
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.6s ease-out',
        'slide-up': 'slideUp 0.8s ease-out',
        'lotus-pulse': 'lotusPulse 3s ease-in-out infinite',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0', transform: 'translateY(20px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        slideUp: {
          '0%': { opacity: '0', transform: 'translateY(40px)' },
          '100%': { opacity: '1', transform: 'translateY(0)' },
        },
        lotusPulse: {
          '0%, 100%': { opacity: '0.6', transform: 'scale(1)' },
          '50%': { opacity: '1', transform: 'scale(1.05)' },
        },
      },
    },
  },
  plugins: [],
}